#!/usr/bin/env python3
"""
Test script to verify YouTube video processing functionality
"""
import sys
import os
import asyncio
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_youtube_processing():
    """Test YouTube video processing"""
    try:
        # Import required modules
        from app.services.gemini_service import GeminiService
        from app.services.youtube_service import YouTubeService
        from app.core.config import settings
        
        print("✅ All imports successful")
        print(f"✅ Using Gemini model: {settings.GEMINI_MODEL}")
        print(f"✅ API key configured: {'Yes' if settings.GEMINI_API_KEY else 'No'}")
        
        # Test YouTube service
        youtube_service = YouTubeService()
        test_url = "https://www.youtube.com/watch?v=Cpl373FOntY&ab_channel=RedBull"
        
        print(f"\n🔍 Testing YouTube URL: {test_url}")
        
        # Extract video ID
        video_id = youtube_service.extract_video_id(test_url)
        print(f"✅ Video ID extracted: {video_id}")
        
        # Get video info
        video_info = await youtube_service.get_video_info(video_id)
        print(f"✅ Video info retrieved:")
        print(f"   Title: {video_info.get('title', 'Unknown')}")
        print(f"   Duration: {video_info.get('duration', 'Unknown')} seconds")
        
        # Test Gemini service
        gemini_service = GeminiService()
        print(f"✅ Gemini service initialized")
        
        # Test transcript retrieval
        try:
            transcript_data = youtube_service.get_transcript(video_id)
            print(f"✅ Transcript retrieved: {len(transcript_data['text'])} characters")
            
            # Test video analysis
            analysis = await gemini_service.analyze_video_content(
                transcript_data['text'][:1000],  # First 1000 chars
                video_info
            )
            print(f"✅ Video analysis completed: {analysis['status']}")
            
        except Exception as e:
            print(f"⚠️  Transcript/analysis failed: {e}")
        
        print("\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_youtube_processing())
    sys.exit(0 if success else 1)
