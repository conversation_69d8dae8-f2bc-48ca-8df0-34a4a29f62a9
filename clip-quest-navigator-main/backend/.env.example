# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///./videochat.db

# Application Settings
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://localhost:8081", "http://localhost:8083"]
MAX_VIDEO_SIZE_MB=500
FRAME_EXTRACTION_INTERVAL=5

# Video Processing Limits
MAX_VIDEO_DURATION_SECONDS=3600
MAX_FRAMES_PER_VIDEO=300

# Vector Database Configuration (Optional)
CHROMA_PERSIST_DIRECTORY=./chroma_db
VECTOR_SEARCH_ENABLED=True

# YouTube Integration (Optional)
YOUTUBE_API_KEY=your_youtube_api_key_here

# Model Configuration
GEMINI_MODEL=gemini-2.5-flash
# Alternative models:
# GEMINI_MODEL=gemini-2.5-pro-preview-0506
# GEMINI_MODEL=gemini-2.0-flash-exp

# Performance Settings
BATCH_PROCESSING_ENABLED=True
ENHANCED_FEATURES_ENABLED=True

# Logging
LOG_LEVEL=INFO
