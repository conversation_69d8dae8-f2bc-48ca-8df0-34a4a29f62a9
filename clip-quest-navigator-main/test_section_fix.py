#!/usr/bin/env python3
"""
Test script to verify that section generation now works for the full video duration
without being limited to 25 minutes due to transcript truncation.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.gemini_service import GeminiService
import asyncio

async def test_section_generation():
    """Test section generation with a long transcript"""
    
    # Create a mock long transcript (simulating 49 minutes of content)
    # Each word represents roughly 0.5 seconds, so 49 minutes = 2940 seconds = ~5880 words
    mock_transcript = """
    Welcome to this comprehensive training session on superhuman coding models at Cursor.
    Today we'll be diving deep into the world of AI-assisted development and how these
    advanced models are revolutionizing the way we write, debug, and optimize code.
    
    In this extensive presentation, we'll cover multiple aspects of modern AI development
    tools, starting with the fundamentals and progressing to advanced techniques that
    can dramatically improve your productivity as a developer.
    
    """ * 200  # Repeat to create a long transcript
    
    # Add more content to simulate different sections throughout the video
    sections_content = [
        "Introduction and overview of AI coding assistants",
        "Setting up your development environment with Cursor",
        "Basic features and getting started with AI suggestions",
        "Advanced code completion and intelligent refactoring",
        "Debugging techniques with AI assistance",
        "Code review and optimization strategies",
        "Integration with existing workflows and tools",
        "Best practices for AI-assisted development",
        "Common pitfalls and how to avoid them",
        "Advanced features and customization options",
        "Performance optimization and efficiency tips",
        "Collaborative development with AI tools",
        "Future trends and upcoming features",
        "Q&A session and practical demonstrations",
        "Conclusion and next steps for implementation"
    ]
    
    # Create a realistic long transcript
    full_transcript = ""
    for i, section in enumerate(sections_content):
        # Add timestamp-like content for each section
        section_content = f"""
        At the {i*3 + 1} minute mark, we're discussing {section}.
        This is a detailed explanation that covers various aspects of the topic.
        We'll explore practical examples, demonstrate key concepts, and provide
        hands-on guidance that you can immediately apply in your development work.
        The discussion includes real-world scenarios, best practices, and common
        challenges that developers face when implementing these techniques.
        """ * 50  # Make each section substantial
        
        full_transcript += section_content
    
    print(f"Generated transcript length: {len(full_transcript)} characters")
    print(f"Estimated words: {len(full_transcript.split())} words")
    print(f"Estimated duration: {len(full_transcript.split()) * 0.5 / 60:.1f} minutes")
    
    # Test the section generation
    gemini_service = GeminiService()
    
    video_info = {
        "title": "Training superhuman coding models at Cursor",
        "duration": 2940  # 49 minutes in seconds
    }
    
    print("\nTesting section generation...")
    try:
        sections = await gemini_service.generate_video_sections(full_transcript, video_info)
        
        print(f"\nGenerated {len(sections)} sections:")
        for i, section in enumerate(sections, 1):
            print(f"\n{i}. {section.get('title', 'Untitled')}")
            print(f"   Time: {section.get('start_time', '0:00')} - {section.get('end_time', '0:00')}")
            print(f"   Description: {section.get('description', 'No description')[:100]}...")
            
            # Check if sections extend beyond 25 minutes
            start_time = section.get('start_time', '0:00')
            if ':' in start_time:
                minutes = int(start_time.split(':')[0])
                if minutes > 25:
                    print(f"   ✅ SUCCESS: Section extends beyond 25 minutes (at {minutes} minutes)")
        
        # Check if we have sections covering the full duration
        if sections:
            last_section = sections[-1]
            last_end_time = last_section.get('end_time', '0:00')
            if ':' in last_end_time:
                last_minutes = int(last_end_time.split(':')[0])
                print(f"\nLast section ends at: {last_minutes} minutes")
                if last_minutes >= 40:  # Should cover most of the 49-minute video
                    print("✅ SUCCESS: Sections cover the full video duration!")
                else:
                    print("❌ ISSUE: Sections don't cover the full video duration")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_section_generation())
