#!/usr/bin/env python3
"""
Test script to verify video duration processing fixes
"""
import asyncio
import sys
import os
sys.path.append('backend')

from app.services.video_processor import VideoProcessor
from app.services.optimized_video_processor import OptimizedVideoProcessor

async def test_video_processing():
    """Test video processing with different processors"""
    
    # Find a test video file
    test_video = None
    upload_dirs = ['uploads', 'backend/uploads']
    
    for upload_dir in upload_dirs:
        if os.path.exists(upload_dir):
            for file in os.listdir(upload_dir):
                if file.endswith(('.mp4', '.avi', '.mov', '.mkv', '.webm')):
                    test_video = os.path.join(upload_dir, file)
                    break
            if test_video:
                break
    
    if not test_video:
        print("❌ No test video found in uploads directory")
        return
    
    print(f"🎬 Testing video: {test_video}")
    
    # Test regular processor
    print("\n📹 Testing Regular VideoProcessor...")
    regular_processor = VideoProcessor()
    regular_info = regular_processor._get_video_info(test_video)
    print(f"   Duration: {regular_info.get('duration', 0):.1f} seconds")
    print(f"   FPS: {regular_info.get('fps', 0):.1f}")
    print(f"   Total Frames: {regular_info.get('total_frames', 0)}")
    
    # Test optimized processor
    print("\n⚡ Testing Optimized VideoProcessor...")
    optimized_processor = OptimizedVideoProcessor()
    optimized_info = await optimized_processor._get_video_info_fast(test_video)
    print(f"   Duration: {optimized_info.get('duration', 0):.1f} seconds")
    print(f"   FPS: {optimized_info.get('fps', 0):.1f}")
    print(f"   Frame Count: {optimized_info.get('frame_count', 0)}")
    print(f"   Max Duration Setting: {optimized_processor.max_duration} seconds")
    print(f"   Max Frames Setting: {optimized_processor.max_frames_per_video}")
    
    # Test frame extraction strategy
    duration = optimized_info.get('duration', 0)
    if duration > 600:  # 10 minutes
        target_frame_count = min(optimized_processor.max_frames_per_video, max(30, int(duration / 10)))
        strategy = "1 frame per 10 seconds (long video)"
    else:
        target_frame_count = min(optimized_processor.max_frames_per_video, max(10, int(duration / 3)))
        strategy = "1 frame per 3 seconds (short video)"
    
    print(f"   Frame Extraction Strategy: {strategy}")
    print(f"   Target Frame Count: {target_frame_count}")
    
    # Check if duration would be truncated
    effective_max_duration = optimized_processor.max_duration
    if duration > effective_max_duration:
        print(f"   ⚠️  Video would be TRUNCATED from {duration:.1f}s to {effective_max_duration}s")
    else:
        print(f"   ✅ Video duration is within limits")

if __name__ == "__main__":
    asyncio.run(test_video_processing())
